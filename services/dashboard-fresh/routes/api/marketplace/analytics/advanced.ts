// Marketplace Advanced Analytics API Proxy
// Integrates existing enhanced analytics (cohort, CLV, funnel, predictive) for marketplace context

import { defineRoute } from "$fresh/server.ts";
import { AppState } from "../../../../types/fresh.ts";

interface AdvancedAnalyticsRequest {
  analytics_type: 'cohort' | 'clv' | 'funnel' | 'predictive';
  time_range?: string;
  segment?: string;
  prediction_type?: 'churn' | 'revenue' | 'behavior' | 'anomaly';
}

interface AdvancedAnalyticsResponse {
  success: boolean;
  data: {
    cohort_analysis?: any;
    clv_analysis?: any;
    funnel_analysis?: any;
    predictive_analysis?: any;
  };
  metadata: {
    generated_at: string;
    tenant_id: string;
    analytics_type: string;
    performance_metrics: {
      query_time_ms: number;
      data_points: number;
    };
  };
}

export default defineRoute<AppState>(async (req, ctx) => {
  const user = ctx.state.user;
  
  // Authentication check
  if (!user) {
    return new Response(JSON.stringify({ error: "Unauthorized" }), {
      status: 401,
      headers: { "Content-Type": "application/json" },
    });
  }

  // Marketplace access check
  if (!user.roles?.includes('marketplace_participant')) {
    return new Response(JSON.stringify({ error: "Insufficient permissions" }), {
      status: 403,
      headers: { "Content-Type": "application/json" },
    });
  }

  const tenantId = user.tenantId || user.tenant_id;
  if (!tenantId) {
    return new Response(JSON.stringify({ error: "Missing tenant ID" }), {
      status: 400,
      headers: { "Content-Type": "application/json" },
    });
  }

  try {
    const url = new URL(req.url);
    const analyticsType = url.searchParams.get("type") as 'cohort' | 'clv' | 'funnel' | 'predictive';
    const timeRange = url.searchParams.get("range") || "30d";
    const segment = url.searchParams.get("segment") || "all";
    const predictionType = url.searchParams.get("prediction_type") as 'churn' | 'revenue' | 'behavior' | 'anomaly';

    if (!analyticsType) {
      return new Response(JSON.stringify({ error: "Analytics type required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    const startTime = performance.now();
    const baseUrl = Deno.env.get("API_BASE_URL") || "http://localhost:8001";
    const token = ctx.state.token || "dev-mock-token";
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'X-Tenant-ID': tenantId,
      'Content-Type': 'application/json'
    };

    let analyticsData: any = {};
    let dataPoints = 0;

    // Route to appropriate enhanced analytics endpoint
    switch (analyticsType) {
      case 'cohort':
        const cohortResponse = await fetch(
          `${baseUrl}/api/enhanced-analytics/cohorts/analysis?tenant_id=${tenantId}&date_range=${timeRange}&cohort_type=acquisition&granularity=monthly&include_projections=true`,
          { headers }
        );
        if (cohortResponse.ok) {
          const cohortData = await cohortResponse.json();
          analyticsData.cohort_analysis = cohortData.data;
          dataPoints += cohortData.data?.segments?.length || 0;
        }
        break;

      case 'clv':
        const clvResponse = await fetch(
          `${baseUrl}/api/enhanced-analytics/clv/calculations?tenant_id=${tenantId}&segment=${segment}&prediction_horizon=12m&include_trends=true`,
          { headers }
        );
        if (clvResponse.ok) {
          const clvData = await clvResponse.json();
          analyticsData.clv_analysis = clvData.data;
          dataPoints += clvData.data?.clvAnalysis?.totalCustomers || 0;
        }
        break;

      case 'funnel':
        const funnelResponse = await fetch(
          `${baseUrl}/api/enhanced-analytics/funnels/conversion-steps?tenant_id=${tenantId}&time_range=${timeRange}&include_optimization=true`,
          { headers }
        );
        if (funnelResponse.ok) {
          const funnelData = await funnelResponse.json();
          analyticsData.funnel_analysis = funnelData.data;
          dataPoints += funnelData.data?.steps?.length || 0;
        }
        break;

      case 'predictive':
        if (!predictionType) {
          return new Response(JSON.stringify({ error: "Prediction type required for predictive analytics" }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
          });
        }

        const predictiveResponse = await fetch(
          `${baseUrl}/api/enhanced-analytics/predictions/${predictionType}?tenant_id=${tenantId}&include_confidence=true&include_explanation=true`,
          { headers }
        );
        if (predictiveResponse.ok) {
          const predictiveData = await predictiveResponse.json();
          analyticsData.predictive_analysis = predictiveData.data;
          dataPoints += predictiveData.data?.predictions?.length || 0;
        }
        break;

      default:
        return new Response(JSON.stringify({ error: "Invalid analytics type" }), {
          status: 400,
          headers: { "Content-Type": "application/json" },
        });
    }

    const queryTime = performance.now() - startTime;

    const response: AdvancedAnalyticsResponse = {
      success: true,
      data: analyticsData,
      metadata: {
        generated_at: new Date().toISOString(),
        tenant_id: tenantId,
        analytics_type: analyticsType,
        performance_metrics: {
          query_time_ms: Math.round(queryTime * 100) / 100,
          data_points: dataPoints
        }
      }
    };

    console.log(`Advanced analytics ${analyticsType} completed in ${queryTime.toFixed(2)}ms for tenant ${tenantId}`);

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: { 
        "Content-Type": "application/json",
        "Cache-Control": "public, max-age=300" // 5 minute cache
      },
    });

  } catch (error) {
    console.error('Error fetching advanced analytics:', error);
    
    return new Response(JSON.stringify({ 
      success: false,
      error: "Failed to fetch advanced analytics",
      message: error.message 
    }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
});
