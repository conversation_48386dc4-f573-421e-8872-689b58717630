// Marketplace Analytics Service
// Comprehensive service for fetching and processing marketplace analytics data

export interface MarketplaceAnalyticsOptions {
  tenantId: string;
  timeRange?: string;
  includeAdvanced?: boolean;
  includePredictive?: boolean;
  includeRealtime?: boolean;
}

export interface CohortAnalysisData {
  segments: Array<{
    id: string;
    name: string;
    startDate: string;
    size: number;
    retentionRates: number[];
    revenueData: number[];
  }>;
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    totalCustomers: number;
    totalRevenue: number;
  };
  predictiveInsights: {
    expectedChurn: number;
    revenueProjection: number;
    retentionTrend: 'improving' | 'declining' | 'stable';
  };
}

export interface CLVAnalysisData {
  clvAnalysis: {
    totalCustomers: number;
    averageCLV: number;
    predictedCLV: number;
    segments: Record<string, {
      count: number;
      averageCLV: number;
      predictedCLV: number;
      percentage: number;
    }>;
  };
  trends: Array<{
    month: string;
    averageCLV: number;
    customerCount: number;
  }>;
}

export interface FunnelAnalysisData {
  steps: Array<{
    name: string;
    visitors: number;
    conversions: number;
    conversionRate: number;
    dropOffRate: number;
  }>;
  overview: {
    totalVisitors: number;
    totalConversions: number;
    overallConversionRate: number;
    biggestDropOff: string;
  };
  optimization: {
    recommendations: Array<{
      step: string;
      issue: string;
      recommendation: string;
      potentialImpact: number;
    }>;
  };
}

export interface PredictiveAnalysisData {
  churn: {
    predictions: Array<{
      customerId: string;
      churnProbability: number;
      riskLevel: 'low' | 'medium' | 'high';
      factors: string[];
    }>;
    summary: {
      totalAtRisk: number;
      averageChurnProbability: number;
      topRiskFactors: string[];
    };
  };
  revenue: {
    forecast: Array<{
      period: string;
      predictedRevenue: number;
      confidenceInterval: [number, number];
    }>;
    accuracy: {
      mape: number;
      rmse: number;
      lastUpdated: string;
    };
  };
}

export interface ComprehensiveMarketplaceAnalytics {
  partnership_metrics: any[];
  network_trends: any[];
  tenant_activity: any[];
  realtime_performance: any[];
  summary_stats: any;
  advanced_analytics?: {
    cohort_analysis?: CohortAnalysisData;
    clv_analysis?: CLVAnalysisData;
    funnel_analysis?: FunnelAnalysisData;
    predictive_analysis?: PredictiveAnalysisData;
  };
  performance_metrics: {
    total_query_time_ms: number;
    data_freshness: string;
    cache_hit_rate?: number;
  };
}

export class MarketplaceAnalyticsService {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl?: string, token?: string, tenantId?: string) {
    this.baseUrl = baseUrl || Deno.env.get("API_BASE_URL") || "http://localhost:8001";
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...(tenantId && { 'X-Tenant-ID': tenantId })
    };
  }

  async fetchComprehensiveAnalytics(options: MarketplaceAnalyticsOptions): Promise<ComprehensiveMarketplaceAnalytics> {
    const startTime = performance.now();
    const { tenantId, timeRange = "30d", includeAdvanced = true, includePredictive = true, includeRealtime = true } = options;

    try {
      // Fetch basic marketplace metrics
      const basicMetricsPromises = [
        this.fetchPartnershipMetrics(tenantId, timeRange),
        this.fetchNetworkTrends(tenantId, timeRange),
        this.fetchTenantActivity(tenantId, "24h"),
        ...(includeRealtime ? [this.fetchRealtimePerformance(tenantId, "2h")] : [])
      ];

      // Fetch advanced analytics if requested
      const advancedPromises = includeAdvanced ? [
        this.fetchCohortAnalysis(tenantId, timeRange),
        this.fetchCLVAnalysis(tenantId, timeRange),
        this.fetchFunnelAnalysis(tenantId, timeRange),
        ...(includePredictive ? [this.fetchPredictiveAnalysis(tenantId)] : [])
      ] : [];

      // Execute all requests in parallel
      const [basicResults, advancedResults] = await Promise.allSettled([
        Promise.allSettled(basicMetricsPromises),
        Promise.allSettled(advancedPromises)
      ]);

      // Process basic metrics
      const [partnershipMetrics, networkTrends, tenantActivity, realtimePerformance] = 
        basicResults.status === 'fulfilled' ? basicResults.value : [];

      // Process advanced analytics
      const [cohortAnalysis, clvAnalysis, funnelAnalysis, predictiveAnalysis] = 
        advancedResults.status === 'fulfilled' ? advancedResults.value : [];

      // Build comprehensive response
      const analytics: ComprehensiveMarketplaceAnalytics = {
        partnership_metrics: this.extractData(partnershipMetrics, []),
        network_trends: this.extractData(networkTrends, []),
        tenant_activity: this.extractData(tenantActivity, []),
        realtime_performance: this.extractData(realtimePerformance, []),
        summary_stats: this.calculateSummaryStats(
          this.extractData(partnershipMetrics, []),
          this.extractData(networkTrends, [])
        ),
        performance_metrics: {
          total_query_time_ms: Math.round((performance.now() - startTime) * 100) / 100,
          data_freshness: new Date().toISOString()
        }
      };

      // Add advanced analytics if available
      if (includeAdvanced) {
        analytics.advanced_analytics = {
          ...(cohortAnalysis && { cohort_analysis: this.extractData(cohortAnalysis, null) }),
          ...(clvAnalysis && { clv_analysis: this.extractData(clvAnalysis, null) }),
          ...(funnelAnalysis && { funnel_analysis: this.extractData(funnelAnalysis, null) }),
          ...(includePredictive && predictiveAnalysis && { predictive_analysis: this.extractData(predictiveAnalysis, null) })
        };
      }

      return analytics;

    } catch (error) {
      console.error('Error fetching comprehensive marketplace analytics:', error);
      throw new Error(`Failed to fetch marketplace analytics: ${error.message}`);
    }
  }

  private async fetchPartnershipMetrics(tenantId: string, timeRange: string) {
    const response = await fetch(
      `${this.baseUrl}/api/marketplace/analytics/partnership-metrics?tenant_id=${tenantId}&period=${timeRange}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchNetworkTrends(tenantId: string, timeRange: string) {
    const response = await fetch(
      `${this.baseUrl}/api/marketplace/analytics/network-trends?tenant_id=${tenantId}&period=${timeRange}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchTenantActivity(tenantId: string, timeRange: string) {
    const response = await fetch(
      `${this.baseUrl}/api/marketplace/analytics/tenant-activity?tenant_id=${tenantId}&period=${timeRange}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchRealtimePerformance(tenantId: string, timeRange: string) {
    const response = await fetch(
      `${this.baseUrl}/api/marketplace/analytics/realtime-performance?tenant_id=${tenantId}&period=${timeRange}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchCohortAnalysis(tenantId: string, timeRange: string) {
    const response = await fetch(
      `/api/marketplace/analytics/advanced?type=cohort&range=${timeRange}&tenant_id=${tenantId}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchCLVAnalysis(tenantId: string, timeRange: string) {
    const response = await fetch(
      `/api/marketplace/analytics/advanced?type=clv&range=${timeRange}&segment=all&tenant_id=${tenantId}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchFunnelAnalysis(tenantId: string, timeRange: string) {
    const response = await fetch(
      `/api/marketplace/analytics/advanced?type=funnel&range=${timeRange}&tenant_id=${tenantId}`,
      { headers: this.defaultHeaders }
    );
    return response.ok ? await response.json() : null;
  }

  private async fetchPredictiveAnalysis(tenantId: string) {
    const [churnResponse, revenueResponse] = await Promise.allSettled([
      fetch(`/api/marketplace/analytics/advanced?type=predictive&prediction_type=churn&tenant_id=${tenantId}`, { headers: this.defaultHeaders }),
      fetch(`/api/marketplace/analytics/advanced?type=predictive&prediction_type=revenue&tenant_id=${tenantId}`, { headers: this.defaultHeaders })
    ]);

    const churnData = churnResponse.status === 'fulfilled' && churnResponse.value.ok ? await churnResponse.value.json() : null;
    const revenueData = revenueResponse.status === 'fulfilled' && revenueResponse.value.ok ? await revenueResponse.value.json() : null;

    return {
      churn: churnData?.data?.predictive_analysis,
      revenue: revenueData?.data?.predictive_analysis
    };
  }

  private extractData(result: any, fallback: any) {
    if (!result || result.status !== 'fulfilled') return fallback;
    return result.value?.data || result.value || fallback;
  }

  private calculateSummaryStats(partnershipMetrics: any[], networkTrends: any[]) {
    const totalRevenue7d = networkTrends.reduce((sum, trend) => sum + (trend.daily_revenue || 0), 0);
    const totalEvents7d = networkTrends.reduce((sum, trend) => sum + (trend.daily_events || 0), 0);
    const avgConversionRate = networkTrends.length > 0 
      ? networkTrends.reduce((sum, trend) => sum + (trend.daily_conversion_rate || 0), 0) / networkTrends.length
      : 0;

    return {
      total_partnerships: new Set(partnershipMetrics.map(m => m.partnership_id)).size,
      active_partnerships: new Set(partnershipMetrics.filter(m => m.event_count > 0).map(m => m.partnership_id)).size,
      total_revenue_7d: totalRevenue7d,
      total_events_7d: totalEvents7d,
      avg_conversion_rate: avgConversionRate,
      top_partnerships: partnershipMetrics
        .reduce((acc, metric) => {
          const existing = acc.find(p => p.partnership_id === metric.partnership_id);
          if (existing) {
            existing.total_revenue += metric.total_revenue;
            existing.total_events += metric.event_count;
          } else {
            acc.push({
              partnership_id: metric.partnership_id,
              total_revenue: metric.total_revenue,
              total_events: metric.event_count
            });
          }
          return acc;
        }, [])
        .sort((a, b) => b.total_revenue - a.total_revenue)
        .slice(0, 5)
    };
  }
}
