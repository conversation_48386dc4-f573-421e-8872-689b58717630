// D3.js Marketplace Analytics Dashboard Island
// Advanced interactive visualizations for marketplace analytics with real-time capabilities

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import * as d3 from "d3";
import { User } from "../../utils/auth.ts";

interface CohortSegment {
  id: string;
  name?: string;
  startDate: string;
  size: number;
  retentionRates: number[];
}

interface CohortAnalysisData {
  segments: CohortSegment[];
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
  };
}

interface MarketplaceAnalyticsData {
  partnership_metrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }>;
  network_trends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }>;
  tenant_activity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }>;
  realtime_performance: Array<{
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }>;
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: Array<{
      partnership_id: string;
      total_revenue: number;
      total_events: number;
    }>;
  };
  advanced_analytics?: {
    cohort_analysis?: CohortAnalysisData;
    clv_analysis?: unknown;
    funnel_analysis?: unknown;
    predictive_analysis?: unknown;
  };
}

interface D3MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: MarketplaceAnalyticsData, format: string) => void;
}

export default function D3MarketplaceAnalyticsDashboard({
  analyticsData,
  user,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3MarketplaceAnalyticsDashboardProps) {
  // Refs for D3 chart containers
  const networkTrendsRef = useRef<HTMLDivElement>(null);
  const partnershipMetricsRef = useRef<HTMLDivElement>(null);
  const realtimePerformanceRef = useRef<HTMLDivElement>(null);
  const cohortAnalysisRef = useRef<HTMLDivElement>(null);

  // State management
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [realtimeData, setRealtimeData] = useState<unknown[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Signals for reactive updates
  const chartDimensions = useSignal({ width: 800, height: 400 });
  const isDarkMode = useSignal(false);

  // Real-time streaming connection
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    let eventSource: EventSource | null = null;

    const connectToStream = () => {
      setConnectionStatus('connecting');
      eventSource = new EventSource('/api/marketplace/analytics/stream');

      eventSource.onopen = () => {
        console.log('Connected to real-time analytics stream');
        setConnectionStatus('connected');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'heartbeat') {
            setLastUpdate(new Date().toLocaleTimeString());
            return;
          }

          if (data.event_type) {
            setRealtimeData(prev => [...prev.slice(-19), data]); // Keep last 20 updates
            setLastUpdate(new Date().toLocaleTimeString());

            // Update charts with new data
            if (data.event_type === 'performance_update' || data.event_type === 'revenue_update') {
              updateRealtimeCharts(data);
            }
          }
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Real-time stream error:', error);
        setConnectionStatus('disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isRealTimeEnabled) {
            connectToStream();
          }
        }, 5000);
      };
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
        setConnectionStatus('disconnected');
      }
    };
  }, [isRealTimeEnabled]);

  // Update charts with real-time data
  const updateRealtimeCharts = (data: { data?: { network_performance?: { total_events_1min: number; total_revenue_1min: number; avg_conversion_rate: number } } }) => {
    if (data.data?.network_performance) {
      // Update real-time performance chart
      const newDataPoint = {
        time: new Date(),
        events_15min: data.data.network_performance.total_events_1min * 15, // Simulate 15-min aggregation
        revenue_15min: data.data.network_performance.total_revenue_1min * 15,
        conversion_rate_15min: data.data.network_performance.avg_conversion_rate
      };

      // Add to existing realtime_performance data
      analyticsData.realtime_performance = [
        ...analyticsData.realtime_performance.slice(-19), // Keep last 19 points
        {
          quarter_hour: newDataPoint.time.toISOString(),
          partnership_id: 'realtime-aggregate',
          events_15min: newDataPoint.events_15min,
          revenue_15min: newDataPoint.revenue_15min,
          conversion_rate_15min: newDataPoint.conversion_rate_15min
        }
      ];

      // Re-render the real-time chart
      initializeRealtimePerformanceChart();
    }
  };

  // Initialize charts on mount and data changes
  useEffect(() => {
    if (analyticsData) {
      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
      if (analyticsData.advanced_analytics?.cohort_analysis) {
        initializeCohortAnalysisChart();
      }
    }
  }, [analyticsData, selectedTimeFrame, selectedMetric]);

  // Handle responsive chart resizing
  useEffect(() => {
    const handleResize = () => {
      const container = networkTrendsRef.current?.parentElement;
      if (container) {
        chartDimensions.value = {
          width: Math.max(600, container.clientWidth - 40),
          height: 400
        };
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial sizing

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Network Trends Chart (Line Chart with Multiple Metrics)
  const initializeNetworkTrendsChart = () => {
    if (!networkTrendsRef.current || !analyticsData.network_trends.length) return;

    const container = d3.select(networkTrendsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = chartDimensions.value.height - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Parse dates and prepare data
    const data = analyticsData.network_trends.map(d => ({
      ...d,
      date: new Date(d.day)
    })).sort((a, b) => a.date.getTime() - b.date.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events) || 0])
      .range([height, 0]);

    // Line generator
    const line = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .curve(d3.curveMonotoneX);

    // Add axes with proper labels
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%m/%d")(d as Date)));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => {
        const value = Number(d);
        if (selectedMetric === 'revenue') {
          return d3.format("$,.0s")(value);
        } else {
          return d3.format(".2s")(value);
        }
      }));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Date");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -40)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text(selectedMetric === 'revenue' ? 'Daily Revenue ($)' :
            selectedMetric === 'events' ? 'Daily Events' : 'Daily Conversions');

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3B82F6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots for data points
    g.selectAll(".dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .attr("r", 4)
      .attr("fill", "#3B82F6")
      .on("mouseover", function(event, d) {
        // Tooltip implementation
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("opacity", 0)
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.8)")
          .style("color", "white")
          .style("padding", "8px")
          .style("border-radius", "4px")
          .style("font-size", "12px");

        tooltip.transition().duration(200).style("opacity", .9);
        tooltip.html(`
          <strong>${d.day}</strong><br/>
          Revenue: $${d.daily_revenue.toLocaleString()}<br/>
          Events: ${d.daily_events.toLocaleString()}<br/>
          Conversion: ${d.daily_conversion_rate.toFixed(2)}%
        `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 28) + "px");
      })
      .on("mouseout", function() {
        d3.selectAll(".tooltip").remove();
      });

    // Add chart title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text(`Network Trends - ${selectedMetric === 'revenue' ? 'Revenue' : 'Events'}`);
  };

  // Partnership Metrics Chart (Horizontal Bar Chart)
  const initializePartnershipMetricsChart = () => {
    if (!partnershipMetricsRef.current || !analyticsData.summary_stats.top_partnerships.length) return;

    const container = d3.select(partnershipMetricsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 120 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = analyticsData.summary_stats.top_partnerships.slice(0, 5);

    // Scales
    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.total_revenue) || 0])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.partnership_id))
      .range([0, height])
      .padding(0.1);

    // Add bars with enhanced interactivity
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.partnership_id) || 0)
      .attr("width", d => xScale(d.total_revenue))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#10B981")
      .style("cursor", "pointer")
      .on("mouseover", function(_event, d) {
        d3.select(this).attr("fill", "#059669");

        // Enhanced tooltip
        const tooltip = d3.select("body").append("div")
          .attr("class", "partnership-tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.9)")
          .style("color", "white")
          .style("padding", "12px")
          .style("border-radius", "6px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000");

        tooltip.html(`
          <div class="font-semibold mb-2">${d.partnership_id}</div>
          <div>Revenue: <span class="font-semibold text-green-300">$${d.total_revenue.toLocaleString()}</span></div>
          <div>Events: <span class="font-semibold">${d.total_events.toLocaleString()}</span></div>
          <div>Avg. per Event: <span class="font-semibold">$${(d.total_revenue / d.total_events).toFixed(2)}</span></div>
        `)
          .style("left", (_event.pageX + 10) + "px")
          .style("top", (_event.pageY - 10) + "px")
          .style("opacity", 0)
          .transition()
          .duration(200)
          .style("opacity", 1);
      })
      .on("mouseout", function() {
        d3.select(this).attr("fill", "#10B981");
        d3.selectAll(".partnership-tooltip").remove();
      })
      .on("click", function(_event, d) {
        console.log(`Clicked partnership: ${d.partnership_id}, Revenue: $${d.total_revenue.toLocaleString()}`);
      });

    // Add value labels with currency formatting
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.total_revenue) + 5)
      .attr("y", d => (yScale(d.partnership_id) || 0) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text(d => `$${d3.format(".2s")(d.total_revenue)}`);

    // Add axes with proper formatting
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `$${d3.format(".2s")(Number(d))}`));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Total Revenue ($)");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -60)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Partnership ID");

    // Add chart title and subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -25)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Top Partnerships by Revenue");

    g.append("text")
      .attr("x", width / 2)
      .attr("y", -8)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text(`Top ${data.length} performing partnerships in ${selectedTimeFrame}`);
  };

  // Real-time Performance Chart (Area Chart)
  const initializeRealtimePerformanceChart = () => {
    if (!realtimePerformanceRef.current || !analyticsData.realtime_performance.length) return;

    const container = d3.select(realtimePerformanceRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 250 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Prepare data
    const data = analyticsData.realtime_performance.map(d => ({
      ...d,
      time: new Date(d.quarter_hour)
    })).sort((a, b) => a.time.getTime() - b.time.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.time) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.events_15min) || 0])
      .range([height, 0]);

    interface RealtimeDataPoint {
      time: Date;
      events_15min: number;
      revenue_15min: number;
      conversion_rate_15min: number;
    }

    // Area generator
    const area = d3.area<RealtimeDataPoint>()
      .x(d => xScale(d.time))
      .y0(height)
      .y1(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    // Add area with gradient
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "realtime-area-gradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0).attr("y1", height)
      .attr("x2", 0).attr("y2", 0);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", "#8B5CF6")
      .attr("stop-opacity", 0.1);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", "#8B5CF6")
      .attr("stop-opacity", 0.6);

    g.append("path")
      .datum(data)
      .attr("fill", "url(#realtime-area-gradient)")
      .attr("d", area);

    // Add line
    const line = d3.line<RealtimeDataPoint>()
      .x(d => xScale(d.time))
      .y(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#8B5CF6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add data points with enhanced tooltips
    g.selectAll(".realtime-dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "realtime-dot")
      .attr("cx", d => xScale(d.time))
      .attr("cy", d => yScale(d.events_15min))
      .attr("r", 3)
      .attr("fill", "#8B5CF6")
      .style("cursor", "pointer")
      .on("mouseover", function(event, d) {
        d3.select(this).attr("r", 5).attr("fill", "#7C3AED");

        const tooltip = d3.select("body").append("div")
          .attr("class", "realtime-tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.9)")
          .style("color", "white")
          .style("padding", "12px")
          .style("border-radius", "6px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000");

        tooltip.html(`
          <div class="font-semibold mb-2">${d3.timeFormat("%H:%M")(d.time)}</div>
          <div>Events: <span class="font-semibold text-purple-300">${d.events_15min.toLocaleString()}</span></div>
          <div>Revenue: <span class="font-semibold text-green-300">$${d.revenue_15min.toLocaleString()}</span></div>
          <div>Conversion: <span class="font-semibold text-blue-300">${d.conversion_rate_15min.toFixed(2)}%</span></div>
          <div class="text-xs text-gray-300 mt-1">15-minute interval</div>
        `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 10) + "px")
          .style("opacity", 0)
          .transition()
          .duration(200)
          .style("opacity", 1);
      })
      .on("mouseout", function() {
        d3.select(this).attr("r", 3).attr("fill", "#8B5CF6");
        d3.selectAll(".realtime-tooltip").remove();
      });

    // Add axes with proper formatting
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%H:%M")(d as Date)));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d3.format(".2s")));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Time (15-minute intervals)");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -40)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Events per 15 minutes");

    // Add chart title and subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -25)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Real-time Performance");

    g.append("text")
      .attr("x", width / 2)
      .attr("y", -8)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text("Live marketplace activity in 15-minute intervals");

    // Add live indicator if real-time is enabled
    if (isRealTimeEnabled && connectionStatus === 'connected') {
      g.append("circle")
        .attr("cx", width - 20)
        .attr("cy", -20)
        .attr("r", 4)
        .attr("fill", "#10B981")
        .style("opacity", 0.8);

      g.append("text")
        .attr("x", width - 35)
        .attr("y", -16)
        .attr("text-anchor", "end")
        .style("font-size", "10px")
        .style("font-weight", "500")
        .style("fill", "#10B981")
        .text("LIVE");
    }
  };

  // Cohort Analysis Chart (Heatmap)
  const initializeCohortAnalysisChart = () => {
    if (!cohortAnalysisRef.current) return;

    const container = d3.select(cohortAnalysisRef.current);
    container.selectAll("*").remove();

    // Check if we have cohort data, otherwise use mock data for visualization
    const cohortData = analyticsData.advanced_analytics?.cohort_analysis || generateMockCohortData();

    if (!cohortData || !cohortData.segments || cohortData.segments.length === 0) {
      container.append("div")
        .style("padding", "20px")
        .style("text-align", "center")
        .style("color", "#6B7280")
        .html(`
          <div class="flex items-center justify-center h-32">
            <div class="text-center">
              <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p class="text-sm text-gray-500">No cohort data available</p>
              <p class="text-xs text-gray-400 mt-1">Enable advanced analytics to view cohort analysis</p>
            </div>
          </div>
        `);
      return;
    }

    const margin = { top: 60, right: 40, bottom: 60, left: 80 };
    const width = Math.min(chartDimensions.value.width, 600) - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Prepare heatmap data
    const segments = cohortData.segments.slice(0, 8); // Limit to 8 cohorts for readability
    const maxPeriods = Math.max(...segments.map((s: CohortSegment) => s.retentionRates?.length || 0));
    const periods = Array.from({ length: Math.min(maxPeriods, 12) }, (_, i) => i); // Limit to 12 periods

    // Create scales
    const xScale = d3.scaleBand()
      .domain(periods.map(p => `Period ${p}`))
      .range([0, width])
      .padding(0.05);

    const yScale = d3.scaleBand()
      .domain(segments.map((s: CohortSegment) => s.name || s.id))
      .range([0, height])
      .padding(0.05);

    // Color scale for retention rates
    const colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, 100]);

    // Add chart title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -30)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Cohort Retention Heatmap");

    // Add subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -10)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text("Customer retention rates by cohort over time periods");

    // Create heatmap cells
    segments.forEach((segment: CohortSegment, _segmentIndex: number) => {
      periods.forEach((period: number, _periodIndex: number) => {
        const retentionRate = segment.retentionRates?.[period] || 0;
        const xPos = xScale(`Period ${period}`) || 0;
        const yPos = yScale(segment.name || segment.id) || 0;

        g.append("rect")
          .attr("x", xPos)
          .attr("y", yPos)
          .attr("width", xScale.bandwidth())
          .attr("height", yScale.bandwidth())
          .attr("fill", colorScale(retentionRate * 100))
          .attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB")
          .attr("stroke-width", 1)
          .style("cursor", "pointer")
          .on("mouseover", function(event) {
            // Highlight cell
            d3.select(this).attr("stroke-width", 2).attr("stroke", "#3B82F6");

            // Show tooltip
            const tooltip = d3.select("body").append("div")
              .attr("class", "cohort-tooltip")
              .style("position", "absolute")
              .style("background", "rgba(0, 0, 0, 0.9)")
              .style("color", "white")
              .style("padding", "12px")
              .style("border-radius", "6px")
              .style("font-size", "12px")
              .style("pointer-events", "none")
              .style("z-index", "1000")
              .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

            tooltip.html(`
              <div class="font-semibold mb-2">${segment.name || segment.id}</div>
              <div>Period: ${period}</div>
              <div>Retention Rate: <span class="font-semibold">${(retentionRate * 100).toFixed(1)}%</span></div>
              <div>Cohort Size: ${segment.size?.toLocaleString() || 'N/A'}</div>
              <div class="text-xs text-gray-300 mt-1">Click for details</div>
            `)
              .style("left", (event.pageX + 10) + "px")
              .style("top", (event.pageY - 10) + "px")
              .style("opacity", 0)
              .transition()
              .duration(200)
              .style("opacity", 1);
          })
          .on("mouseout", function() {
            // Remove highlight
            d3.select(this).attr("stroke-width", 1).attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB");

            // Remove tooltip
            d3.selectAll(".cohort-tooltip").remove();
          })
          .on("click", function() {
            console.log(`Clicked cohort: ${segment.name || segment.id}, Period: ${period}, Retention: ${(retentionRate * 100).toFixed(1)}%`);
          });

        // Add text labels for high contrast
        if (retentionRate > 0.3) {
          g.append("text")
            .attr("x", (xScale(`Period ${period}`) || 0) + xScale.bandwidth() / 2)
            .attr("y", (yScale(segment.name || segment.id) || 0) + yScale.bandwidth() / 2)
            .attr("text-anchor", "middle")
            .attr("dy", "0.35em")
            .style("font-size", "10px")
            .style("font-weight", "bold")
            .style("fill", retentionRate > 0.6 ? "white" : "#111827")
            .style("pointer-events", "none")
            .text(`${(retentionRate * 100).toFixed(0)}%`);
        }
      });
    });

    // Add X axis
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add Y axis
    g.append("g")
      .call(d3.axisLeft(yScale))
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 45)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Time Periods");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -60)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Customer Cohorts");

    // Add color legend
    const legendWidth = 200;
    const legendHeight = 10;
    const legendX = width - legendWidth - 20;
    const legendY = -45;

    const legendScale = d3.scaleLinear()
      .domain([0, 100])
      .range([0, legendWidth]);

    const legendAxis = d3.axisBottom(legendScale)
      .ticks(5)
      .tickFormat(d => `${d}%`);

    const legend = g.append("g")
      .attr("transform", `translate(${legendX}, ${legendY})`);

    // Create gradient for legend
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "cohort-legend-gradient")
      .attr("x1", "0%")
      .attr("x2", "100%")
      .attr("y1", "0%")
      .attr("y2", "0%");

    gradient.selectAll("stop")
      .data(d3.range(0, 101, 10))
      .enter().append("stop")
      .attr("offset", d => `${d}%`)
      .attr("stop-color", d => colorScale(d));

    legend.append("rect")
      .attr("width", legendWidth)
      .attr("height", legendHeight)
      .style("fill", "url(#cohort-legend-gradient)")
      .attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB");

    legend.append("g")
      .attr("transform", `translate(0, ${legendHeight})`)
      .call(legendAxis)
      .selectAll("text")
      .style("font-size", "10px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    legend.append("text")
      .attr("x", legendWidth / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "11px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Retention Rate");
  };

  // Generate mock cohort data for demonstration
  const generateMockCohortData = (): CohortAnalysisData => {
    const cohorts: CohortSegment[] = [];
    const cohortNames = ['Jan 2024', 'Feb 2024', 'Mar 2024', 'Apr 2024', 'May 2024', 'Jun 2024'];

    cohortNames.forEach((name, index) => {
      const retentionRates: number[] = [];
      let baseRate = 1.0; // Start at 100%

      for (let period = 0; period < 12; period++) {
        if (period === 0) {
          retentionRates.push(1.0); // 100% at period 0
        } else {
          // Simulate retention decay with some randomness
          baseRate *= (0.85 + Math.random() * 0.1); // 85-95% retention each period
          retentionRates.push(Math.max(0.05, baseRate)); // Minimum 5% retention
        }
      }

      cohorts.push({
        id: `cohort-${index}`,
        name,
        startDate: new Date(2024, index, 1).toISOString(),
        size: Math.floor(Math.random() * 500) + 100,
        retentionRates
      });
    });

    return {
      segments: cohorts,
      overview: {
        totalCohorts: cohorts.length,
        avgRetentionRate: 0.65,
        bestPerformingCohort: cohorts[0]?.name || 'N/A'
      }
    };
  };

  return (
    <div class="d3-marketplace-analytics-dashboard" role="main" aria-label="Marketplace Analytics Dashboard">
      {/* Dashboard Controls */}
      <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center space-x-4">
          <label class="flex flex-col">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</span>
            <select
              value={selectedTimeFrame}
              onChange={(e) => {
                setSelectedTimeFrame(e.currentTarget.value);
                onTimeFrameChange?.(e.currentTarget.value);
              }}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              aria-label="Select time range for analytics data"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </label>

          <label class="flex flex-col">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Metric</span>
            <select
              value={selectedMetric}
              onChange={(e) => {
                setSelectedMetric(e.currentTarget.value);
                onMetricChange?.(e.currentTarget.value);
              }}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              aria-label="Select metric to display in charts"
            >
              <option value="revenue">Revenue</option>
              <option value="events">Events</option>
              <option value="conversions">Conversions</option>
            </select>
          </label>
        </div>

        <div class="flex items-center space-x-3">
          {/* Real-time Status Indicator */}
          <div class="flex items-center space-x-2">
            <div class={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`} aria-hidden="true"></div>
            <span class="text-sm text-gray-600 dark:text-gray-300">
              {connectionStatus === 'connected' ? 'Live' :
               connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
            </span>
            {lastUpdate && (
              <span class="text-xs text-gray-500 dark:text-gray-400">
                Last update: {lastUpdate}
              </span>
            )}
          </div>

          <label class="flex items-center">
            <input
              type="checkbox"
              checked={isRealTimeEnabled}
              onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
              class="mr-2 focus:ring-2 focus:ring-blue-500"
              aria-describedby="realtime-help"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
            <span id="realtime-help" class="sr-only">
              Enable or disable real-time data streaming for live analytics updates
            </span>
          </label>

          <button
            type="button"
            onClick={() => onDataExport?.(analyticsData, 'csv')}
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Export analytics data as CSV file"
          >
            Export Data
          </button>
        </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" role="region" aria-label="Analytics Charts">
        {/* Network Trends Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Network Trends - {selectedMetric === 'revenue' ? 'Revenue' : 'Events'}
          </h3>
          <div
            ref={networkTrendsRef}
            class="w-full"
            role="img"
            aria-label={`Network trends chart showing ${selectedMetric} over ${selectedTimeFrame}`}
            tabIndex={0}
          ></div>
        </div>

        {/* Partnership Metrics Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Partnerships by Revenue
          </h3>
          <div
            ref={partnershipMetricsRef}
            class="w-full"
            role="img"
            aria-label="Bar chart showing top partnerships ranked by revenue performance"
            tabIndex={0}
          ></div>
        </div>

        {/* Real-time Performance Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Performance
            {isRealTimeEnabled && connectionStatus === 'connected' && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Live
              </span>
            )}
          </h3>
          <div
            ref={realtimePerformanceRef}
            class="w-full"
            role="img"
            aria-label="Real-time performance area chart showing live marketplace activity in 15-minute intervals"
            tabIndex={0}
          ></div>
        </div>

        {/* Cohort Analysis Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cohort Analysis
            {analyticsData.advanced_analytics?.cohort_analysis && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Advanced
              </span>
            )}
          </h3>
          <div
            ref={cohortAnalysisRef}
            class="w-full"
            role="img"
            aria-label="Cohort analysis visualization showing customer retention patterns over time"
            tabIndex={0}
          ></div>
        </div>
      </div>

      {/* Performance Metrics */}
      {analyticsData.advanced_analytics && (
        <div class="mt-6 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Advanced Analytics Available</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-medium text-blue-600 dark:text-blue-400">
                {analyticsData.advanced_analytics.cohort_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Cohort Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-green-600 dark:text-green-400">
                {analyticsData.advanced_analytics.clv_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">CLV Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-purple-600 dark:text-purple-400">
                {analyticsData.advanced_analytics.funnel_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Funnel Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-orange-600 dark:text-orange-400">
                {analyticsData.advanced_analytics.predictive_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Predictive Analytics</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
