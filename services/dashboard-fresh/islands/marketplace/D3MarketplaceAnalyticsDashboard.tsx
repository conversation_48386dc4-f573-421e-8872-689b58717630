// D3.js Marketplace Analytics Dashboard Island
// Advanced interactive visualizations for marketplace analytics with real-time capabilities

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import * as d3 from "d3";
import { User } from "../../utils/auth.ts";

// Helper function to calculate retention decay factors based on cohort type
const getRetentionDecayFactor = (
  cohortType: 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic',
  partnershipType?: 'strategic' | 'affiliate' | 'integration'
): number => {
  // Base decay factors by cohort type (higher = better retention)
  const baseFactors = {
    organic: 0.88,
    partnership_acquisition: 0.92,
    cross_tenant_collaboration: 0.95,
    referral_program: 0.90,
    joint_marketing_campaign: 0.89
  };

  // Partnership type multipliers
  const partnershipMultipliers = {
    strategic: 1.05,
    integration: 1.08,
    affiliate: 1.02
  };

  const baseFactor = baseFactors[cohortType];
  const multiplier = partnershipType ? partnershipMultipliers[partnershipType] : 1.0;

  // Add some randomness (±3%) to simulate real-world variation
  const randomVariation = 0.97 + Math.random() * 0.06;

  return Math.min(0.98, baseFactor * multiplier * randomVariation);
};

interface CohortSegment {
  id: string;
  name?: string;
  startDate: string;
  size: number;
  retentionRates: number[];
  cohortType?: 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType?: 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel?: 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  partnershipId?: string;
  partnerName?: string;
  revenueData?: number[];
  conversionRates?: number[];
}

interface MarketplaceCohortComparison {
  vs_organic_retention: number;
  vs_average_partnership: number;
  revenue_multiplier: number;
  performance_tier: 'top_quartile' | 'above_average' | 'average' | 'below_average';
}

interface CohortAnalysisData {
  segments: CohortSegment[];
  overview: {
    totalCohorts: number;
    avgRetentionRate: number;
    bestPerformingCohort: string;
    organicRetentionBaseline: number;
    partnershipRetentionAverage: number;
  };
  partnership_cohorts?: {
    partnership_id: string;
    partner_name: string;
    acquisition_channel: 'referral' | 'collaboration' | 'cross_promotion';
    segments: CohortSegment[];
    comparative_metrics: MarketplaceCohortComparison;
  }[];
  cross_tenant_analysis?: {
    tenant_pair: string;
    shared_customer_retention: number[];
    collaboration_effectiveness: number;
  }[];
}

interface CohortFilters {
  cohortType: 'all' | 'partnership_acquisition' | 'cross_tenant_collaboration' | 'referral_program' | 'joint_marketing_campaign' | 'organic';
  partnershipType: 'all' | 'strategic' | 'affiliate' | 'integration';
  acquisitionChannel: 'all' | 'referral' | 'collaboration' | 'cross_promotion' | 'direct';
  timeRange: '30d' | '90d' | '180d' | '1y';
  comparisonMode: 'vs_organic' | 'vs_average' | 'vs_top_quartile' | 'absolute';
}

interface MarketplaceAnalyticsData {
  partnership_metrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }>;
  network_trends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }>;
  tenant_activity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }>;
  realtime_performance: Array<{
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }>;
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: Array<{
      partnership_id: string;
      total_revenue: number;
      total_events: number;
    }>;
  };
  advanced_analytics?: {
    cohort_analysis?: CohortAnalysisData;
    clv_analysis?: unknown;
    funnel_analysis?: unknown;
    predictive_analysis?: unknown;
  };
}

interface D3MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: MarketplaceAnalyticsData, format: string) => void;
}

// Generate mock cohort data for demonstration
const generateMockCohortData = (): CohortAnalysisData => {
  console.log('Generating enhanced marketplace cohort data...');

  const cohorts: CohortSegment[] = [];

  // Define marketplace-specific cohort configurations
  const cohortConfigs = [
    // Organic baseline cohorts
    { name: 'Organic Jan 2024', type: 'organic' as const, channel: 'direct' as const, partnershipType: undefined, baseRetention: 0.58, size: 450 },
    { name: 'Organic Feb 2024', type: 'organic' as const, channel: 'direct' as const, partnershipType: undefined, baseRetention: 0.60, size: 520 },

    // Strategic partnership cohorts (higher retention)
    { name: 'Strategic Partner A', type: 'partnership_acquisition' as const, channel: 'collaboration' as const, partnershipType: 'strategic' as const, baseRetention: 0.78, size: 320, partnerId: 'strategic-001', partnerName: 'TechCorp Solutions' },
    { name: 'Strategic Partner B', type: 'partnership_acquisition' as const, channel: 'referral' as const, partnershipType: 'strategic' as const, baseRetention: 0.82, size: 280, partnerId: 'strategic-002', partnerName: 'Enterprise Systems Inc' },

    // Affiliate partnership cohorts (moderate retention)
    { name: 'Affiliate Network Q1', type: 'referral_program' as const, channel: 'referral' as const, partnershipType: 'affiliate' as const, baseRetention: 0.68, size: 180, partnerId: 'affiliate-001', partnerName: 'Marketing Affiliates' },
    { name: 'Affiliate Network Q2', type: 'referral_program' as const, channel: 'cross_promotion' as const, partnershipType: 'affiliate' as const, baseRetention: 0.65, size: 220, partnerId: 'affiliate-002', partnerName: 'Growth Partners' },

    // Integration partnership cohorts (high retention)
    { name: 'Integration Partner', type: 'cross_tenant_collaboration' as const, channel: 'collaboration' as const, partnershipType: 'integration' as const, baseRetention: 0.85, size: 150, partnerId: 'integration-001', partnerName: 'Platform Integrators' },

    // Joint marketing campaigns (variable retention)
    { name: 'Joint Campaign Q1', type: 'joint_marketing_campaign' as const, channel: 'cross_promotion' as const, partnershipType: 'strategic' as const, baseRetention: 0.72, size: 380, partnerId: 'campaign-001', partnerName: 'Marketing Alliance' },
    { name: 'Joint Campaign Q2', type: 'joint_marketing_campaign' as const, channel: 'collaboration' as const, partnershipType: 'affiliate' as const, baseRetention: 0.69, size: 290, partnerId: 'campaign-002', partnerName: 'Co-Marketing Group' }
  ];

  cohortConfigs.forEach((config, index) => {
    const retentionRates: number[] = [];
    const revenueData: number[] = [];
    const conversionRates: number[] = [];
    let currentRetention = 1.0; // Start at 100%
    let currentRevenue = config.size * (50 + Math.random() * 100); // Initial revenue per cohort

    for (let period = 0; period < 12; period++) {
      if (period === 0) {
        retentionRates.push(1.0);
        revenueData.push(currentRevenue);
        conversionRates.push(0.15 + Math.random() * 0.1); // 15-25% initial conversion
      } else {
        // Calculate retention decay based on cohort type
        const decayFactor = getRetentionDecayFactor(config.type, config.partnershipType);
        currentRetention *= decayFactor;

        // Apply baseline retention adjustment
        const adjustedRetention = Math.max(0.05, currentRetention * (config.baseRetention / 0.65));
        retentionRates.push(adjustedRetention);

        // Calculate revenue based on retention and cohort performance
        currentRevenue *= (adjustedRetention + 0.1); // Revenue follows retention with slight premium
        revenueData.push(currentRevenue);

        // Conversion rates tend to improve over time for retained customers
        const conversionRate = Math.min(0.35, (0.15 + period * 0.02) * (adjustedRetention + 0.2));
        conversionRates.push(conversionRate);
      }
    }

    cohorts.push({
      id: `cohort-${index}`,
      name: config.name,
      startDate: new Date(2024, index % 6, 1).toISOString(),
      size: config.size,
      retentionRates,
      revenueData,
      conversionRates,
      cohortType: config.type,
      partnershipType: config.partnershipType,
      acquisitionChannel: config.channel,
      partnershipId: config.partnerId,
      partnerName: config.partnerName
    });
  });

  // Calculate enhanced overview metrics
  const organicCohorts = cohorts.filter(c => c.cohortType === 'organic');
  const partnershipCohorts = cohorts.filter(c => c.cohortType !== 'organic');

  const organicRetentionBaseline = organicCohorts.length > 0
    ? organicCohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / organicCohorts.length // 6-month retention
    : 0.58;

  const partnershipRetentionAverage = partnershipCohorts.length > 0
    ? partnershipCohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / partnershipCohorts.length
    : 0.72;

  const bestPerformingCohort = cohorts.reduce((best, current) =>
    current.retentionRates[6] > best.retentionRates[6] ? current : best
  );

  const mockData: CohortAnalysisData = {
    segments: cohorts,
    overview: {
      totalCohorts: cohorts.length,
      avgRetentionRate: cohorts.reduce((sum, c) => sum + c.retentionRates[6], 0) / cohorts.length,
      bestPerformingCohort: bestPerformingCohort.name || 'N/A',
      organicRetentionBaseline,
      partnershipRetentionAverage
    },
    partnership_cohorts: partnershipCohorts.map(cohort => ({
      partnership_id: cohort.partnershipId || cohort.id,
      partner_name: cohort.partnerName || cohort.name || 'Unknown Partner',
      acquisition_channel: cohort.acquisitionChannel as 'referral' | 'collaboration' | 'cross_promotion',
      segments: [cohort],
      comparative_metrics: {
        vs_organic_retention: cohort.retentionRates[6] / organicRetentionBaseline,
        vs_average_partnership: cohort.retentionRates[6] / partnershipRetentionAverage,
        revenue_multiplier: (cohort.revenueData?.[6] || 0) / (organicCohorts[0]?.revenueData?.[6] || 1),
        performance_tier: getPerformanceTier(cohort.retentionRates[6], partnershipRetentionAverage)
      }
    })),
    cross_tenant_analysis: [
      {
        tenant_pair: 'TechCorp-Enterprise',
        shared_customer_retention: [1.0, 0.92, 0.87, 0.83, 0.79, 0.76, 0.73],
        collaboration_effectiveness: 1.15
      },
      {
        tenant_pair: 'Marketing-Growth',
        shared_customer_retention: [1.0, 0.88, 0.81, 0.75, 0.70, 0.66, 0.62],
        collaboration_effectiveness: 1.08
      }
    ]
  };

  console.log('Generated enhanced marketplace cohort data:', {
    segmentCount: mockData.segments.length,
    partnershipCount: mockData.partnership_cohorts?.length || 0,
    organicBaseline: organicRetentionBaseline.toFixed(3),
    partnershipAverage: partnershipRetentionAverage.toFixed(3),
    bestPerformer: bestPerformingCohort.name,
    overview: mockData.overview
  });

  return mockData;
};

// Helper function to determine performance tier
const getPerformanceTier = (retention: number, average: number): 'top_quartile' | 'above_average' | 'average' | 'below_average' => {
  const ratio = retention / average;
  if (ratio >= 1.25) return 'top_quartile';
  if (ratio >= 1.1) return 'above_average';
  if (ratio >= 0.9) return 'average';
  return 'below_average';
};

export default function D3MarketplaceAnalyticsDashboard({
  analyticsData,
  user: _user,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3MarketplaceAnalyticsDashboardProps) {
  // Refs for D3 chart containers
  const networkTrendsRef = useRef<HTMLDivElement>(null);
  const partnershipMetricsRef = useRef<HTMLDivElement>(null);
  const realtimePerformanceRef = useRef<HTMLDivElement>(null);
  const cohortAnalysisRef = useRef<HTMLDivElement>(null);

  // State management
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [_loading, _setLoading] = useState(false);
  const [_realtimeData, setRealtimeData] = useState<unknown[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Enhanced cohort filtering state
  const [cohortFilters, setCohortFilters] = useState<CohortFilters>({
    cohortType: 'all',
    partnershipType: 'all',
    acquisitionChannel: 'all',
    timeRange: '180d',
    comparisonMode: 'vs_organic'
  });
  const [showComparison, setShowComparison] = useState(false);

  // Signals for reactive updates
  const chartDimensions = useSignal({ width: 800, height: 400 });
  const isDarkMode = useSignal(false);

  // Real-time streaming connection
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    let eventSource: EventSource | null = null;

    const connectToStream = () => {
      setConnectionStatus('connecting');
      eventSource = new EventSource('/api/marketplace/analytics/stream');

      eventSource.onopen = () => {
        console.log('Connected to real-time analytics stream');
        setConnectionStatus('connected');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'heartbeat') {
            setLastUpdate(new Date().toLocaleTimeString());
            return;
          }

          if (data.event_type) {
            setRealtimeData(prev => [...prev.slice(-19), data]); // Keep last 20 updates
            setLastUpdate(new Date().toLocaleTimeString());

            // Update charts with new data
            if (data.event_type === 'performance_update' || data.event_type === 'revenue_update') {
              updateRealtimeCharts(data);
            }
          }
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Real-time stream error:', error);
        setConnectionStatus('disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isRealTimeEnabled) {
            connectToStream();
          }
        }, 5000);
      };
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
        setConnectionStatus('disconnected');
      }
    };
  }, [isRealTimeEnabled]);

  // Update charts with real-time data
  const updateRealtimeCharts = (data: { data?: { network_performance?: { total_events_1min: number; total_revenue_1min: number; avg_conversion_rate: number } } }) => {
    if (data.data?.network_performance) {
      // Update real-time performance chart
      const newDataPoint = {
        time: new Date(),
        events_15min: data.data.network_performance.total_events_1min * 15, // Simulate 15-min aggregation
        revenue_15min: data.data.network_performance.total_revenue_1min * 15,
        conversion_rate_15min: data.data.network_performance.avg_conversion_rate
      };

      // Add to existing realtime_performance data
      analyticsData.realtime_performance = [
        ...analyticsData.realtime_performance.slice(-19), // Keep last 19 points
        {
          quarter_hour: newDataPoint.time.toISOString(),
          partnership_id: 'realtime-aggregate',
          events_15min: newDataPoint.events_15min,
          revenue_15min: newDataPoint.revenue_15min,
          conversion_rate_15min: newDataPoint.conversion_rate_15min
        }
      ];

      // Re-render the real-time chart
      initializeRealtimePerformanceChart();
    }
  };

  // Initialize charts on mount and data changes
  useEffect(() => {
    if (analyticsData) {
      console.log('Initializing charts with analytics data:', {
        hasNetworkTrends: analyticsData.network_trends?.length > 0,
        hasPartnershipMetrics: analyticsData.summary_stats?.top_partnerships?.length > 0,
        hasRealtimePerformance: analyticsData.realtime_performance?.length > 0,
        hasAdvancedAnalytics: !!analyticsData.advanced_analytics,
        hasCohortAnalysis: !!analyticsData.advanced_analytics?.cohort_analysis,
        cohortFilters: cohortFilters
      });

      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
      // Always initialize cohort analysis chart - it has built-in fallback to mock data
      initializeCohortAnalysisChart();
    }
  }, [analyticsData, selectedTimeFrame, selectedMetric, cohortFilters, showComparison]);

  // Handle responsive chart resizing
  useEffect(() => {
    const handleResize = () => {
      const container = networkTrendsRef.current?.parentElement;
      if (container) {
        chartDimensions.value = {
          width: Math.max(600, container.clientWidth - 40),
          height: 400
        };
      }
    };

    globalThis.addEventListener('resize', handleResize);
    handleResize(); // Initial sizing

    return () => globalThis.removeEventListener('resize', handleResize);
  }, []);

  // Network Trends Chart (Line Chart with Multiple Metrics)
  const initializeNetworkTrendsChart = () => {
    if (!networkTrendsRef.current || !analyticsData.network_trends.length) return;

    const container = d3.select(networkTrendsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = chartDimensions.value.height - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    interface NetworkTrendDataPoint {
      date: Date;
      daily_events: number;
      daily_revenue: number;
      unique_customers: number;
      daily_conversion_rate: number;
      day: string;
    }

    // Parse dates and prepare data
    const data: NetworkTrendDataPoint[] = analyticsData.network_trends.map(d => ({
      ...d,
      date: new Date(d.day)
    })).sort((a, b) => a.date.getTime() - b.date.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events) || 0])
      .range([height, 0]);

    // Line generator
    const line = d3.line<NetworkTrendDataPoint>()
      .x(d => xScale(d.date))
      .y(d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .curve(d3.curveMonotoneX);

    // Add axes with proper labels
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%m/%d")(d as Date)));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d => {
        const value = Number(d);
        if (selectedMetric === 'revenue') {
          return d3.format("$,.0s")(value);
        } else {
          return d3.format(".2s")(value);
        }
      }));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Date");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -40)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text(selectedMetric === 'revenue' ? 'Daily Revenue ($)' :
            selectedMetric === 'events' ? 'Daily Events' : 'Daily Conversions');

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3B82F6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots for data points
    g.selectAll(".dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .attr("r", 4)
      .attr("fill", "#3B82F6")
      .on("mouseover", function(event, d) {
        // Tooltip implementation
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("opacity", 0)
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.8)")
          .style("color", "white")
          .style("padding", "8px")
          .style("border-radius", "4px")
          .style("font-size", "12px");

        tooltip.transition().duration(200).style("opacity", .9);
        tooltip.html(`
          <strong>${d.day}</strong><br/>
          Revenue: $${d.daily_revenue.toLocaleString()}<br/>
          Events: ${d.daily_events.toLocaleString()}<br/>
          Conversion: ${d.daily_conversion_rate.toFixed(2)}%
        `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 28) + "px");
      })
      .on("mouseout", function() {
        d3.selectAll(".tooltip").remove();
      });

    // Add chart title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text(`Network Trends - ${selectedMetric === 'revenue' ? 'Revenue' : 'Events'}`);
  };

  // Partnership Metrics Chart (Horizontal Bar Chart)
  const initializePartnershipMetricsChart = () => {
    if (!partnershipMetricsRef.current || !analyticsData.summary_stats.top_partnerships.length) return;

    const container = d3.select(partnershipMetricsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 120 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = analyticsData.summary_stats.top_partnerships.slice(0, 5);

    // Scales
    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.total_revenue) || 0])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.partnership_id))
      .range([0, height])
      .padding(0.1);

    // Add bars with enhanced interactivity
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.partnership_id) || 0)
      .attr("width", d => xScale(d.total_revenue))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#10B981")
      .style("cursor", "pointer")
      .on("mouseover", function(_event, d) {
        d3.select(this).attr("fill", "#059669");

        // Enhanced tooltip
        const tooltip = d3.select("body").append("div")
          .attr("class", "partnership-tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.9)")
          .style("color", "white")
          .style("padding", "12px")
          .style("border-radius", "6px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000");

        tooltip.html(`
          <div class="font-semibold mb-2">${d.partnership_id}</div>
          <div>Revenue: <span class="font-semibold text-green-300">$${d.total_revenue.toLocaleString()}</span></div>
          <div>Events: <span class="font-semibold">${d.total_events.toLocaleString()}</span></div>
          <div>Avg. per Event: <span class="font-semibold">$${(d.total_revenue / d.total_events).toFixed(2)}</span></div>
        `)
          .style("left", (_event.pageX + 10) + "px")
          .style("top", (_event.pageY - 10) + "px")
          .style("opacity", 0)
          .transition()
          .duration(200)
          .style("opacity", 1);
      })
      .on("mouseout", function() {
        d3.select(this).attr("fill", "#10B981");
        d3.selectAll(".partnership-tooltip").remove();
      })
      .on("click", function(_event, d) {
        console.log(`Clicked partnership: ${d.partnership_id}, Revenue: $${d.total_revenue.toLocaleString()}`);
      });

    // Add value labels with currency formatting
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.total_revenue) + 5)
      .attr("y", d => (yScale(d.partnership_id) || 0) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "11px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text(d => `$${d3.format(".2s")(d.total_revenue)}`);

    // Add axes with proper formatting
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d => `$${d3.format(".2s")(Number(d))}`));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Total Revenue ($)");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -60)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Partnership ID");

    // Add chart title and subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -25)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Top Partnerships by Revenue");

    g.append("text")
      .attr("x", width / 2)
      .attr("y", -8)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text(`Top ${data.length} performing partnerships in ${selectedTimeFrame}`);
  };

  // Real-time Performance Chart (Area Chart)
  const initializeRealtimePerformanceChart = () => {
    if (!realtimePerformanceRef.current || !analyticsData.realtime_performance.length) return;

    const container = d3.select(realtimePerformanceRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 250 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Prepare data
    const data = analyticsData.realtime_performance.map(d => ({
      ...d,
      time: new Date(d.quarter_hour)
    })).sort((a, b) => a.time.getTime() - b.time.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.time) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.events_15min) || 0])
      .range([height, 0]);

    interface RealtimeDataPoint {
      time: Date;
      events_15min: number;
      revenue_15min: number;
      conversion_rate_15min: number;
    }

    // Area generator
    const area = d3.area<RealtimeDataPoint>()
      .x(d => xScale(d.time))
      .y0(height)
      .y1(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    // Add area with gradient
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "realtime-area-gradient")
      .attr("gradientUnits", "userSpaceOnUse")
      .attr("x1", 0).attr("y1", height)
      .attr("x2", 0).attr("y2", 0);

    gradient.append("stop")
      .attr("offset", "0%")
      .attr("stop-color", "#8B5CF6")
      .attr("stop-opacity", 0.1);

    gradient.append("stop")
      .attr("offset", "100%")
      .attr("stop-color", "#8B5CF6")
      .attr("stop-opacity", 0.6);

    g.append("path")
      .datum(data)
      .attr("fill", "url(#realtime-area-gradient)")
      .attr("d", area);

    // Add line
    const line = d3.line<RealtimeDataPoint>()
      .x(d => xScale(d.time))
      .y(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#8B5CF6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add data points with enhanced tooltips
    g.selectAll(".realtime-dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "realtime-dot")
      .attr("cx", d => xScale(d.time))
      .attr("cy", d => yScale(d.events_15min))
      .attr("r", 3)
      .attr("fill", "#8B5CF6")
      .style("cursor", "pointer")
      .on("mouseover", function(event, d) {
        d3.select(this).attr("r", 5).attr("fill", "#7C3AED");

        const tooltip = d3.select("body").append("div")
          .attr("class", "realtime-tooltip")
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.9)")
          .style("color", "white")
          .style("padding", "12px")
          .style("border-radius", "6px")
          .style("font-size", "12px")
          .style("pointer-events", "none")
          .style("z-index", "1000");

        tooltip.html(`
          <div class="font-semibold mb-2">${d3.timeFormat("%H:%M")(d.time)}</div>
          <div>Events: <span class="font-semibold text-purple-300">${d.events_15min.toLocaleString()}</span></div>
          <div>Revenue: <span class="font-semibold text-green-300">$${d.revenue_15min.toLocaleString()}</span></div>
          <div>Conversion: <span class="font-semibold text-blue-300">${d.conversion_rate_15min.toFixed(2)}%</span></div>
          <div class="text-xs text-gray-300 mt-1">15-minute interval</div>
        `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 10) + "px")
          .style("opacity", 0)
          .transition()
          .duration(200)
          .style("opacity", 1);
      })
      .on("mouseout", function() {
        d3.select(this).attr("r", 3).attr("fill", "#8B5CF6");
        d3.selectAll(".realtime-tooltip").remove();
      });

    // Add axes with proper formatting
    const xAxis = g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%H:%M")(d as Date)));

    xAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    const yAxis = g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d3.format(".2s")));

    yAxis.selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 35)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Time (15-minute intervals)");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -40)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Events per 15 minutes");

    // Add chart title and subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -25)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Real-time Performance");

    g.append("text")
      .attr("x", width / 2)
      .attr("y", -8)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text("Live marketplace activity in 15-minute intervals");

    // Add live indicator if real-time is enabled
    if (isRealTimeEnabled && connectionStatus === 'connected') {
      g.append("circle")
        .attr("cx", width - 20)
        .attr("cy", -20)
        .attr("r", 4)
        .attr("fill", "#10B981")
        .style("opacity", 0.8);

      g.append("text")
        .attr("x", width - 35)
        .attr("y", -16)
        .attr("text-anchor", "end")
        .style("font-size", "10px")
        .style("font-weight", "500")
        .style("fill", "#10B981")
        .text("LIVE");
    }
  };

  // Cohort Analysis Chart (Heatmap)
  const initializeCohortAnalysisChart = () => {
    console.log('Initializing cohort analysis chart...');

    if (!cohortAnalysisRef.current) {
      console.warn('Cohort analysis ref is not available');
      return;
    }

    const container = d3.select(cohortAnalysisRef.current);
    container.selectAll("*").remove();

    // Check if we have cohort data, otherwise use mock data for visualization
    const realCohortData = analyticsData.advanced_analytics?.cohort_analysis;
    const cohortData = realCohortData || generateMockCohortData();

    console.log('Cohort data status:', {
      hasRealData: !!realCohortData,
      usingMockData: !realCohortData,
      cohortDataExists: !!cohortData,
      segmentsCount: cohortData?.segments?.length || 0
    });

    if (!cohortData || !cohortData.segments || cohortData.segments.length === 0) {
      console.warn('No cohort data available, showing placeholder');

      container.append("div")
        .style("padding", "20px")
        .style("text-align", "center")
        .style("color", "#6B7280")
        .html(`
          <div class="flex items-center justify-center h-32">
            <div class="text-center">
              <svg class="w-12 h-12 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p class="text-sm text-gray-500">No cohort data available</p>
              <p class="text-xs text-gray-400 mt-1">Enable advanced analytics to view cohort analysis</p>
            </div>
          </div>
        `);
      return;
    }

    console.log('Rendering cohort heatmap with', cohortData.segments.length, 'segments');

    const margin = { top: 60, right: 40, bottom: 60, left: 80 };
    const width = Math.min(chartDimensions.value.width, 600) - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    console.log('Chart dimensions:', {
      containerWidth: chartDimensions.value.width,
      chartWidth: width,
      chartHeight: height,
      margin
    });

    try {
      const svg = container
        .append("svg")
        .attr("width", width + margin.left + margin.right)
        .attr("height", height + margin.top + margin.bottom);

      const g = svg.append("g")
        .attr("transform", `translate(${margin.left},${margin.top})`);

    // Prepare heatmap data
    const segments = cohortData.segments.slice(0, 8); // Limit to 8 cohorts for readability
    const maxPeriods = Math.max(...segments.map((s: CohortSegment) => s.retentionRates?.length || 0));
    const periods = Array.from({ length: Math.min(maxPeriods, 12) }, (_, i) => i); // Limit to 12 periods

    // Create scales
    const xScale = d3.scaleBand()
      .domain(periods.map(p => `Period ${p}`))
      .range([0, width])
      .padding(0.05);

    const yScale = d3.scaleBand()
      .domain(segments.map((s: CohortSegment) => s.name || s.id))
      .range([0, height])
      .padding(0.05);

    // Color scale for retention rates
    const colorScale = d3.scaleSequential(d3.interpolateBlues)
      .domain([0, 100]);

    // Add chart title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -30)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .style("fill", isDarkMode.value ? "#F9FAFB" : "#111827")
      .text("Cohort Retention Heatmap");

    // Add subtitle
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -10)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#6B7280")
      .text("Customer retention rates by cohort over time periods");

    // Create heatmap cells
    segments.forEach((segment: CohortSegment, _segmentIndex: number) => {
      periods.forEach((period: number, _periodIndex: number) => {
        const retentionRate = segment.retentionRates?.[period] || 0;
        const xPos = xScale(`Period ${period}`) || 0;
        const yPos = yScale(segment.name || segment.id) || 0;

        g.append("rect")
          .attr("x", xPos)
          .attr("y", yPos)
          .attr("width", xScale.bandwidth())
          .attr("height", yScale.bandwidth())
          .attr("fill", colorScale(retentionRate * 100))
          .attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB")
          .attr("stroke-width", 1)
          .style("cursor", "pointer")
          .on("mouseover", function(event: MouseEvent) {
            // Highlight cell
            d3.select(this as SVGRectElement).attr("stroke-width", 2).attr("stroke", "#3B82F6");

            // Show tooltip
            const tooltip = d3.select("body").append("div")
              .attr("class", "cohort-tooltip")
              .style("position", "absolute")
              .style("background", "rgba(0, 0, 0, 0.9)")
              .style("color", "white")
              .style("padding", "12px")
              .style("border-radius", "6px")
              .style("font-size", "12px")
              .style("pointer-events", "none")
              .style("z-index", "1000")
              .style("box-shadow", "0 4px 6px rgba(0, 0, 0, 0.1)");

            tooltip.html(`
              <div class="font-semibold mb-2">${segment.name || segment.id}</div>
              <div>Period: ${period}</div>
              <div>Retention Rate: <span class="font-semibold">${(retentionRate * 100).toFixed(1)}%</span></div>
              <div>Cohort Size: ${segment.size?.toLocaleString() || 'N/A'}</div>
              <div class="text-xs text-gray-300 mt-1">Click for details</div>
            `)
              .style("left", (event.pageX + 10) + "px")
              .style("top", (event.pageY - 10) + "px")
              .style("opacity", 0)
              .transition()
              .duration(200)
              .style("opacity", 1);
          })
          .on("mouseout", function() {
            // Remove highlight
            d3.select(this as SVGRectElement).attr("stroke-width", 1).attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB");

            // Remove tooltip
            d3.selectAll(".cohort-tooltip").remove();
          })
          .on("click", function() {
            console.log(`Clicked cohort: ${segment.name || segment.id}, Period: ${period}, Retention: ${(retentionRate * 100).toFixed(1)}%`);
          });

        // Add text labels for high contrast
        if (retentionRate > 0.3) {
          g.append("text")
            .attr("x", (xScale(`Period ${period}`) || 0) + xScale.bandwidth() / 2)
            .attr("y", (yScale(segment.name || segment.id) || 0) + yScale.bandwidth() / 2)
            .attr("text-anchor", "middle")
            .attr("dy", "0.35em")
            .style("font-size", "10px")
            .style("font-weight", "bold")
            .style("fill", retentionRate > 0.6 ? "white" : "#111827")
            .style("pointer-events", "none")
            .text(`${(retentionRate * 100).toFixed(0)}%`);
        }
      });
    });

    // Add X axis
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add Y axis
    g.append("g")
      .call(d3.axisLeft(yScale))
      .selectAll("text")
      .style("font-size", "11px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    // Add axis labels
    g.append("text")
      .attr("x", width / 2)
      .attr("y", height + 45)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Time Periods");

    g.append("text")
      .attr("transform", "rotate(-90)")
      .attr("x", -height / 2)
      .attr("y", -60)
      .attr("text-anchor", "middle")
      .style("font-size", "12px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Customer Cohorts");

    // Add color legend
    const legendWidth = 200;
    const legendHeight = 10;
    const legendX = width - legendWidth - 20;
    const legendY = -45;

    const legendScale = d3.scaleLinear()
      .domain([0, 100])
      .range([0, legendWidth]);

    const legendAxis = d3.axisBottom(legendScale)
      .ticks(5)
      .tickFormat(d => `${d}%`);

    const legend = g.append("g")
      .attr("transform", `translate(${legendX}, ${legendY})`);

    // Create gradient for legend
    const gradient = svg.append("defs")
      .append("linearGradient")
      .attr("id", "cohort-legend-gradient")
      .attr("x1", "0%")
      .attr("x2", "100%")
      .attr("y1", "0%")
      .attr("y2", "0%");

    gradient.selectAll("stop")
      .data(d3.range(0, 101, 10))
      .enter().append("stop")
      .attr("offset", d => `${d}%`)
      .attr("stop-color", d => colorScale(d));

    legend.append("rect")
      .attr("width", legendWidth)
      .attr("height", legendHeight)
      .style("fill", "url(#cohort-legend-gradient)")
      .attr("stroke", isDarkMode.value ? "#374151" : "#E5E7EB");

    legend.append("g")
      .attr("transform", `translate(0, ${legendHeight})`)
      .call(legendAxis)
      .selectAll("text")
      .style("font-size", "10px")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151");

    legend.append("text")
      .attr("x", legendWidth / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "11px")
      .style("font-weight", "500")
      .style("fill", isDarkMode.value ? "#D1D5DB" : "#374151")
      .text("Retention Rate");

    } catch (error) {
      console.error('Error rendering cohort heatmap:', error);

      // Show error message
      container.append("div")
        .style("padding", "20px")
        .style("text-align", "center")
        .style("color", "#EF4444")
        .html(`
          <div class="flex items-center justify-center h-32">
            <div class="text-center">
              <svg class="w-12 h-12 mx-auto text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p class="text-sm text-red-500">Error rendering cohort analysis</p>
              <p class="text-xs text-red-400 mt-1">Check console for details</p>
            </div>
          </div>
        `);
    }
  };



  return (
    <div class="d3-marketplace-analytics-dashboard" role="main" aria-label="Marketplace Analytics Dashboard">
      {/* Dashboard Controls */}
      <div class="mb-6 space-y-4">
        {/* Primary Controls */}
        <div class="flex flex-wrap items-center justify-between gap-4">
          <div class="flex items-center space-x-4">
            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</span>
              <select
                value={selectedTimeFrame}
                onChange={(e) => {
                  setSelectedTimeFrame(e.currentTarget.value);
                  onTimeFrameChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select time range for analytics data"
              >
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Metric</span>
              <select
                value={selectedMetric}
                onChange={(e) => {
                  setSelectedMetric(e.currentTarget.value);
                  onMetricChange?.(e.currentTarget.value);
                }}
                class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                aria-label="Select metric to display in charts"
              >
                <option value="revenue">Revenue</option>
                <option value="events">Events</option>
                <option value="conversions">Conversions</option>
              </select>
            </label>
          </div>

          <div class="flex items-center space-x-3">
            {/* Real-time Status Indicator */}
            <div class="flex items-center space-x-2">
              <div class={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
              }`} aria-hidden="true"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">
                {connectionStatus === 'connected' ? 'Live' :
                 connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
              </span>
              {lastUpdate && (
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  Last update: {lastUpdate}
                </span>
              )}
            </div>

            <label class="flex items-center">
              <input
                type="checkbox"
                checked={isRealTimeEnabled}
                onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
                aria-describedby="realtime-help"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
              <span id="realtime-help" class="sr-only">
                Enable or disable real-time data streaming for live analytics updates
              </span>
            </label>

            <button
              type="button"
              onClick={() => onDataExport?.(analyticsData, 'csv')}
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Export analytics data as CSV file"
            >
              Export Data
            </button>
          </div>
        </div>

        {/* Enhanced Cohort Filtering Controls */}
        <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Cohort Analysis Filters</h3>
            <label class="flex items-center">
              <input
                type="checkbox"
                checked={showComparison}
                onChange={(e) => setShowComparison(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Show Comparison View</span>
            </label>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Cohort Type</span>
              <select
                value={cohortFilters.cohortType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, cohortType: e.currentTarget.value as CohortFilters['cohortType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="organic">Organic</option>
                <option value="partnership_acquisition">Partnership Acquisition</option>
                <option value="cross_tenant_collaboration">Cross-Tenant Collaboration</option>
                <option value="referral_program">Referral Program</option>
                <option value="joint_marketing_campaign">Joint Marketing</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Partnership Type</span>
              <select
                value={cohortFilters.partnershipType}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, partnershipType: e.currentTarget.value as CohortFilters['partnershipType'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Partnerships</option>
                <option value="strategic">Strategic</option>
                <option value="affiliate">Affiliate</option>
                <option value="integration">Integration</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Acquisition Channel</span>
              <select
                value={cohortFilters.acquisitionChannel}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, acquisitionChannel: e.currentTarget.value as CohortFilters['acquisitionChannel'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="all">All Channels</option>
                <option value="direct">Direct</option>
                <option value="referral">Referral</option>
                <option value="collaboration">Collaboration</option>
                <option value="cross_promotion">Cross Promotion</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Analysis Period</span>
              <select
                value={cohortFilters.timeRange}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, timeRange: e.currentTarget.value as CohortFilters['timeRange'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="30d">30 Days</option>
                <option value="90d">90 Days</option>
                <option value="180d">180 Days</option>
                <option value="1y">1 Year</option>
              </select>
            </label>

            <label class="flex flex-col">
              <span class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Comparison Mode</span>
              <select
                value={cohortFilters.comparisonMode}
                onChange={(e) => setCohortFilters(prev => ({ ...prev, comparisonMode: e.currentTarget.value as CohortFilters['comparisonMode'] }))}
                class="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-1 focus:ring-blue-500"
              >
                <option value="absolute">Absolute Values</option>
                <option value="vs_organic">vs Organic</option>
                <option value="vs_average">vs Average</option>
                <option value="vs_top_quartile">vs Top Quartile</option>
              </select>
            </label>
          </div>
        </div>
      </div>

          <div class="flex items-center space-x-3">
            {/* Real-time Status Indicator */}
            <div class="flex items-center space-x-2">
              <div class={`w-3 h-3 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-500' :
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
              }`} aria-hidden="true"></div>
              <span class="text-sm text-gray-600 dark:text-gray-300">
                {connectionStatus === 'connected' ? 'Live' :
                 connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
              </span>
              {lastUpdate && (
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  Last update: {lastUpdate}
                </span>
              )}
            </div>

            <label class="flex items-center">
              <input
                type="checkbox"
                checked={isRealTimeEnabled}
                onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
                class="mr-2 focus:ring-2 focus:ring-blue-500"
                aria-describedby="realtime-help"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
              <span id="realtime-help" class="sr-only">
                Enable or disable real-time data streaming for live analytics updates
              </span>
            </label>

            <button
              type="button"
              onClick={() => onDataExport?.(analyticsData, 'csv')}
              class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              aria-label="Export analytics data as CSV file"
            >
              Export Data
            </button>
          </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" role="region" aria-label="Analytics Charts">
        {/* Network Trends Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Network Trends - {selectedMetric === 'revenue' ? 'Revenue' : 'Events'}
          </h3>
          <div
            ref={networkTrendsRef}
            class="w-full"
            role="img"
            aria-label={`Network trends chart showing ${selectedMetric} over ${selectedTimeFrame}`}
            tabIndex={0}
          ></div>
        </div>

        {/* Partnership Metrics Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Partnerships by Revenue
          </h3>
          <div
            ref={partnershipMetricsRef}
            class="w-full"
            role="img"
            aria-label="Bar chart showing top partnerships ranked by revenue performance"
            tabIndex={0}
          ></div>
        </div>

        {/* Real-time Performance Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Performance
            {isRealTimeEnabled && connectionStatus === 'connected' && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Live
              </span>
            )}
          </h3>
          <div
            ref={realtimePerformanceRef}
            class="w-full"
            role="img"
            aria-label="Real-time performance area chart showing live marketplace activity in 15-minute intervals"
            tabIndex={0}
          ></div>
        </div>

        {/* Cohort Analysis Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cohort Analysis
            {analyticsData.advanced_analytics?.cohort_analysis && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Advanced
              </span>
            )}
          </h3>
          <div
            ref={cohortAnalysisRef}
            class="w-full"
            role="img"
            aria-label="Cohort analysis visualization showing customer retention patterns over time"
            tabIndex={0}
          ></div>
        </div>
      </div>

      {/* Enhanced Analytics Status */}
      {analyticsData.advanced_analytics && (
        <div class="mt-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Marketplace Analytics Status</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-medium text-blue-600 dark:text-blue-400">
                {analyticsData.advanced_analytics?.cohort_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Cohort Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-green-600 dark:text-green-400">
                {analyticsData.advanced_analytics?.clv_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">CLV Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-purple-600 dark:text-purple-400">
                {analyticsData.advanced_analytics?.funnel_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Funnel Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-orange-600 dark:text-orange-400">
                {analyticsData.advanced_analytics?.predictive_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Predictive Analytics</div>
            </div>
          </div>

          {/* Partnership Performance Summary */}
          {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts && (
            <div class="mt-4 pt-4 border-t border-blue-200 dark:border-blue-700">
              <div class="flex items-center justify-between text-sm">
                <span class="text-gray-600 dark:text-gray-300">
                  Partnership Performance vs Organic:
                  <span class="font-semibold text-green-600 dark:text-green-400 ml-1">
                    +{(((analyticsData.advanced_analytics?.cohort_analysis?.overview.partnershipRetentionAverage || 0.72) / (analyticsData.advanced_analytics?.cohort_analysis?.overview.organicRetentionBaseline || 0.58) - 1) * 100).toFixed(1)}%
                  </span>
                </span>
                <span class="text-gray-600 dark:text-gray-300">
                  Active Partnerships:
                  <span class="font-semibold text-blue-600 dark:text-blue-400 ml-1">
                    {analyticsData.advanced_analytics?.cohort_analysis?.partnership_cohorts?.length || 0}
                  </span>
                </span>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
