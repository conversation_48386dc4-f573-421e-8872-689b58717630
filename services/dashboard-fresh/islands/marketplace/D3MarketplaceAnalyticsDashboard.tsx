// D3.js Marketplace Analytics Dashboard Island
// Advanced interactive visualizations for marketplace analytics with real-time capabilities

import { useEffect, useRef, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";
import * as d3 from "d3";
import { User } from "../../utils/auth.ts";

interface MarketplaceAnalyticsData {
  partnership_metrics: Array<{
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }>;
  network_trends: Array<{
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }>;
  tenant_activity: Array<{
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }>;
  realtime_performance: Array<{
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }>;
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: Array<{
      partnership_id: string;
      total_revenue: number;
      total_events: number;
    }>;
  };
  advanced_analytics?: {
    cohort_analysis?: any;
    clv_analysis?: any;
    funnel_analysis?: any;
    predictive_analysis?: any;
  };
}

interface D3MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
  onTimeFrameChange?: (timeFrame: string) => void;
  onMetricChange?: (metric: string) => void;
  onDataExport?: (data: any, format: string) => void;
}

export default function D3MarketplaceAnalyticsDashboard({
  analyticsData,
  user,
  onTimeFrameChange,
  onMetricChange,
  onDataExport
}: D3MarketplaceAnalyticsDashboardProps) {
  // Refs for D3 chart containers
  const networkTrendsRef = useRef<HTMLDivElement>(null);
  const partnershipMetricsRef = useRef<HTMLDivElement>(null);
  const realtimePerformanceRef = useRef<HTMLDivElement>(null);
  const cohortAnalysisRef = useRef<HTMLDivElement>(null);

  // State management
  const [selectedTimeFrame, setSelectedTimeFrame] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [realtimeData, setRealtimeData] = useState<any[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Signals for reactive updates
  const chartDimensions = useSignal({ width: 800, height: 400 });
  const isDarkMode = useSignal(false);

  // Real-time streaming connection
  useEffect(() => {
    if (!isRealTimeEnabled) return;

    let eventSource: EventSource | null = null;

    const connectToStream = () => {
      setConnectionStatus('connecting');
      eventSource = new EventSource('/api/marketplace/analytics/stream');

      eventSource.onopen = () => {
        console.log('Connected to real-time analytics stream');
        setConnectionStatus('connected');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          if (data.type === 'heartbeat') {
            setLastUpdate(new Date().toLocaleTimeString());
            return;
          }

          if (data.event_type) {
            setRealtimeData(prev => [...prev.slice(-19), data]); // Keep last 20 updates
            setLastUpdate(new Date().toLocaleTimeString());

            // Update charts with new data
            if (data.event_type === 'performance_update' || data.event_type === 'revenue_update') {
              updateRealtimeCharts(data);
            }
          }
        } catch (error) {
          console.error('Error parsing real-time data:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('Real-time stream error:', error);
        setConnectionStatus('disconnected');

        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isRealTimeEnabled) {
            connectToStream();
          }
        }, 5000);
      };
    };

    connectToStream();

    return () => {
      if (eventSource) {
        eventSource.close();
        setConnectionStatus('disconnected');
      }
    };
  }, [isRealTimeEnabled]);

  // Update charts with real-time data
  const updateRealtimeCharts = (data: any) => {
    if (data.data.network_performance) {
      // Update real-time performance chart
      const newDataPoint = {
        time: new Date(),
        events_15min: data.data.network_performance.total_events_1min * 15, // Simulate 15-min aggregation
        revenue_15min: data.data.network_performance.total_revenue_1min * 15,
        conversion_rate_15min: data.data.network_performance.avg_conversion_rate
      };

      // Add to existing realtime_performance data
      analyticsData.realtime_performance = [
        ...analyticsData.realtime_performance.slice(-19), // Keep last 19 points
        {
          quarter_hour: newDataPoint.time.toISOString(),
          partnership_id: 'realtime-aggregate',
          events_15min: newDataPoint.events_15min,
          revenue_15min: newDataPoint.revenue_15min,
          conversion_rate_15min: newDataPoint.conversion_rate_15min
        }
      ];

      // Re-render the real-time chart
      initializeRealtimePerformanceChart();
    }
  };

  // Initialize charts on mount and data changes
  useEffect(() => {
    if (analyticsData) {
      initializeNetworkTrendsChart();
      initializePartnershipMetricsChart();
      initializeRealtimePerformanceChart();
      if (analyticsData.advanced_analytics?.cohort_analysis) {
        initializeCohortAnalysisChart();
      }
    }
  }, [analyticsData, selectedTimeFrame, selectedMetric]);

  // Handle responsive chart resizing
  useEffect(() => {
    const handleResize = () => {
      const container = networkTrendsRef.current?.parentElement;
      if (container) {
        chartDimensions.value = {
          width: Math.max(600, container.clientWidth - 40),
          height: 400
        };
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // Initial sizing

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Network Trends Chart (Line Chart with Multiple Metrics)
  const initializeNetworkTrendsChart = () => {
    if (!networkTrendsRef.current || !analyticsData.network_trends.length) return;

    const container = d3.select(networkTrendsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 80, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = chartDimensions.value.height - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Parse dates and prepare data
    const data = analyticsData.network_trends.map(d => ({
      ...d,
      date: new Date(d.day)
    })).sort((a, b) => a.date.getTime() - b.date.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.date) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events) || 0])
      .range([height, 0]);

    // Line generator
    const line = d3.line<any>()
      .x(d => xScale(d.date))
      .y(d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .curve(d3.curveMonotoneX);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%m/%d")(d as Date)));

    g.append("g")
      .call(d3.axisLeft(yScale).tickFormat(d3.format(".2s")));

    // Add line
    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#3B82F6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add dots for data points
    g.selectAll(".dot")
      .data(data)
      .enter().append("circle")
      .attr("class", "dot")
      .attr("cx", d => xScale(d.date))
      .attr("cy", d => yScale(selectedMetric === 'revenue' ? d.daily_revenue : d.daily_events))
      .attr("r", 4)
      .attr("fill", "#3B82F6")
      .on("mouseover", function(event, d) {
        // Tooltip implementation
        const tooltip = d3.select("body").append("div")
          .attr("class", "tooltip")
          .style("opacity", 0)
          .style("position", "absolute")
          .style("background", "rgba(0, 0, 0, 0.8)")
          .style("color", "white")
          .style("padding", "8px")
          .style("border-radius", "4px")
          .style("font-size", "12px");

        tooltip.transition().duration(200).style("opacity", .9);
        tooltip.html(`
          <strong>${d.day}</strong><br/>
          Revenue: $${d.daily_revenue.toLocaleString()}<br/>
          Events: ${d.daily_events.toLocaleString()}<br/>
          Conversion: ${d.daily_conversion_rate.toFixed(2)}%
        `)
          .style("left", (event.pageX + 10) + "px")
          .style("top", (event.pageY - 28) + "px");
      })
      .on("mouseout", function() {
        d3.selectAll(".tooltip").remove();
      });

    // Add chart title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text(`Network Trends - ${selectedMetric === 'revenue' ? 'Revenue' : 'Events'}`);
  };

  // Partnership Metrics Chart (Horizontal Bar Chart)
  const initializePartnershipMetricsChart = () => {
    if (!partnershipMetricsRef.current || !analyticsData.summary_stats.top_partnerships.length) return;

    const container = d3.select(partnershipMetricsRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 120 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 300 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    const data = analyticsData.summary_stats.top_partnerships.slice(0, 5);

    // Scales
    const xScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.total_revenue) || 0])
      .range([0, width]);

    const yScale = d3.scaleBand()
      .domain(data.map(d => d.partnership_id))
      .range([0, height])
      .padding(0.1);

    // Add bars
    g.selectAll(".bar")
      .data(data)
      .enter().append("rect")
      .attr("class", "bar")
      .attr("x", 0)
      .attr("y", d => yScale(d.partnership_id) || 0)
      .attr("width", d => xScale(d.total_revenue))
      .attr("height", yScale.bandwidth())
      .attr("fill", "#10B981")
      .on("mouseover", function(event, d) {
        d3.select(this).attr("fill", "#059669");
      })
      .on("mouseout", function() {
        d3.select(this).attr("fill", "#10B981");
      });

    // Add value labels
    g.selectAll(".label")
      .data(data)
      .enter().append("text")
      .attr("class", "label")
      .attr("x", d => xScale(d.total_revenue) + 5)
      .attr("y", d => (yScale(d.partnership_id) || 0) + yScale.bandwidth() / 2)
      .attr("dy", "0.35em")
      .style("font-size", "12px")
      .text(d => `$${d.total_revenue.toLocaleString()}`);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat(d3.format(".2s")));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text("Top Partnerships by Revenue");
  };

  // Real-time Performance Chart (Area Chart)
  const initializeRealtimePerformanceChart = () => {
    if (!realtimePerformanceRef.current || !analyticsData.realtime_performance.length) return;

    const container = d3.select(realtimePerformanceRef.current);
    container.selectAll("*").remove();

    const margin = { top: 20, right: 30, bottom: 40, left: 60 };
    const width = chartDimensions.value.width - margin.left - margin.right;
    const height = 250 - margin.top - margin.bottom;

    const svg = container
      .append("svg")
      .attr("width", width + margin.left + margin.right)
      .attr("height", height + margin.top + margin.bottom);

    const g = svg.append("g")
      .attr("transform", `translate(${margin.left},${margin.top})`);

    // Prepare data
    const data = analyticsData.realtime_performance.map(d => ({
      ...d,
      time: new Date(d.quarter_hour)
    })).sort((a, b) => a.time.getTime() - b.time.getTime());

    // Scales
    const xScale = d3.scaleTime()
      .domain(d3.extent(data, d => d.time) as [Date, Date])
      .range([0, width]);

    const yScale = d3.scaleLinear()
      .domain([0, d3.max(data, d => d.events_15min) || 0])
      .range([height, 0]);

    // Area generator
    const area = d3.area<any>()
      .x(d => xScale(d.time))
      .y0(height)
      .y1(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    // Add area
    g.append("path")
      .datum(data)
      .attr("fill", "#8B5CF6")
      .attr("fill-opacity", 0.6)
      .attr("d", area);

    // Add line
    const line = d3.line<any>()
      .x(d => xScale(d.time))
      .y(d => yScale(d.events_15min))
      .curve(d3.curveMonotoneX);

    g.append("path")
      .datum(data)
      .attr("fill", "none")
      .attr("stroke", "#8B5CF6")
      .attr("stroke-width", 2)
      .attr("d", line);

    // Add axes
    g.append("g")
      .attr("transform", `translate(0,${height})`)
      .call(d3.axisBottom(xScale).tickFormat((d) => d3.timeFormat("%H:%M")(d as Date)));

    g.append("g")
      .call(d3.axisLeft(yScale));

    // Add title
    g.append("text")
      .attr("x", width / 2)
      .attr("y", -5)
      .attr("text-anchor", "middle")
      .style("font-size", "16px")
      .style("font-weight", "bold")
      .text("Real-time Performance (15-min intervals)");
  };

  // Cohort Analysis Chart (Heatmap)
  const initializeCohortAnalysisChart = () => {
    if (!cohortAnalysisRef.current || !analyticsData.advanced_analytics?.cohort_analysis) return;

    const container = d3.select(cohortAnalysisRef.current);
    container.selectAll("*").remove();

    // Placeholder for cohort heatmap - will be implemented in next iteration
    container.append("div")
      .style("padding", "20px")
      .style("text-align", "center")
      .style("color", "#6B7280")
      .html(`
        <h3>Cohort Analysis Heatmap</h3>
        <p>Advanced cohort visualization will be rendered here</p>
        <p>Data available: ${analyticsData.advanced_analytics.cohort_analysis ? 'Yes' : 'No'}</p>
      `);
  };

  return (
    <div class="d3-marketplace-analytics-dashboard" role="main" aria-label="Marketplace Analytics Dashboard">
      {/* Dashboard Controls */}
      <div class="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div class="flex items-center space-x-4">
          <label class="flex flex-col">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Time Range</span>
            <select
              value={selectedTimeFrame}
              onChange={(e) => {
                setSelectedTimeFrame(e.currentTarget.value);
                onTimeFrameChange?.(e.currentTarget.value);
              }}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              aria-label="Select time range for analytics data"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </label>

          <label class="flex flex-col">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Metric</span>
            <select
              value={selectedMetric}
              onChange={(e) => {
                setSelectedMetric(e.currentTarget.value);
                onMetricChange?.(e.currentTarget.value);
              }}
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              aria-label="Select metric to display in charts"
            >
              <option value="revenue">Revenue</option>
              <option value="events">Events</option>
              <option value="conversions">Conversions</option>
            </select>
          </label>
        </div>

        <div class="flex items-center space-x-3">
          {/* Real-time Status Indicator */}
          <div class="flex items-center space-x-2">
            <div class={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
            }`} aria-hidden="true"></div>
            <span class="text-sm text-gray-600 dark:text-gray-300">
              {connectionStatus === 'connected' ? 'Live' :
               connectionStatus === 'connecting' ? 'Connecting...' : 'Offline'}
            </span>
            {lastUpdate && (
              <span class="text-xs text-gray-500 dark:text-gray-400">
                Last update: {lastUpdate}
              </span>
            )}
          </div>

          <label class="flex items-center">
            <input
              type="checkbox"
              checked={isRealTimeEnabled}
              onChange={(e) => setIsRealTimeEnabled(e.currentTarget.checked)}
              class="mr-2 focus:ring-2 focus:ring-blue-500"
              aria-describedby="realtime-help"
            />
            <span class="text-sm text-gray-700 dark:text-gray-300">Real-time Updates</span>
            <span id="realtime-help" class="sr-only">
              Enable or disable real-time data streaming for live analytics updates
            </span>
          </label>

          <button
            type="button"
            onClick={() => onDataExport?.(analyticsData, 'csv')}
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm font-medium transition-colors focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            aria-label="Export analytics data as CSV file"
          >
            Export Data
          </button>
        </div>
      </div>

      {/* Charts Grid */}
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" role="region" aria-label="Analytics Charts">
        {/* Network Trends Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Network Trends - {selectedMetric === 'revenue' ? 'Revenue' : 'Events'}
          </h3>
          <div
            ref={networkTrendsRef}
            class="w-full"
            role="img"
            aria-label={`Network trends chart showing ${selectedMetric} over ${selectedTimeFrame}`}
            tabIndex={0}
          ></div>
        </div>

        {/* Partnership Metrics Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Top Partnerships by Revenue
          </h3>
          <div
            ref={partnershipMetricsRef}
            class="w-full"
            role="img"
            aria-label="Bar chart showing top partnerships ranked by revenue performance"
            tabIndex={0}
          ></div>
        </div>

        {/* Real-time Performance Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Real-time Performance
            {isRealTimeEnabled && connectionStatus === 'connected' && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                Live
              </span>
            )}
          </h3>
          <div
            ref={realtimePerformanceRef}
            class="w-full"
            role="img"
            aria-label="Real-time performance area chart showing live marketplace activity in 15-minute intervals"
            tabIndex={0}
          ></div>
        </div>

        {/* Cohort Analysis Chart */}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Cohort Analysis
            {analyticsData.advanced_analytics?.cohort_analysis && (
              <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Advanced
              </span>
            )}
          </h3>
          <div
            ref={cohortAnalysisRef}
            class="w-full"
            role="img"
            aria-label="Cohort analysis visualization showing customer retention patterns over time"
            tabIndex={0}
          ></div>
        </div>
      </div>

      {/* Performance Metrics */}
      {analyticsData.advanced_analytics && (
        <div class="mt-6 bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">Advanced Analytics Available</h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="font-medium text-blue-600 dark:text-blue-400">
                {analyticsData.advanced_analytics.cohort_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Cohort Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-green-600 dark:text-green-400">
                {analyticsData.advanced_analytics.clv_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">CLV Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-purple-600 dark:text-purple-400">
                {analyticsData.advanced_analytics.funnel_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Funnel Analysis</div>
            </div>
            <div class="text-center">
              <div class="font-medium text-orange-600 dark:text-orange-400">
                {analyticsData.advanced_analytics.predictive_analysis ? '✓' : '✗'}
              </div>
              <div class="text-gray-600 dark:text-gray-300">Predictive Analytics</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
